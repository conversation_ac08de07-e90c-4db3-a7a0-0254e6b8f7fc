import {
  IconBrowserCheck,
  IconChecklist,
  IconLayoutDashboard,
  IconNotification,
  IconPalette,
  IconSettings,
  IconTool,
  IconUserCog,
  IconUsers,
} from '@tabler/icons-react'
import { AudioWaveform, Command, GalleryVerticalEnd } from 'lucide-react'
import { type SidebarData } from '../types'

export const sidebarData: SidebarData = {
  teams: [
    {
      name: 'Shadcn Admin',
      logo: Command,
      plan: '数据中心',
    },
    {
      name: 'Acme Inc',
      logo: GalleryVerticalEnd,
      plan: 'Enterprise',
    },
    {
      name: 'Acme Corp.',
      logo: AudioWaveform,
      plan: 'Startup',
    },
  ],
  navGroups: [
    {
      title: '通用页面',
      items: [
        {
          title: '主页',
          url: '/',
          icon: IconLayoutDashboard,
        },
        {
          title: '任务管理',
          url: '/tasks',
          icon: IconChecklist,
        },
      ],
    },
    {
      title: '系统管理',
      items: [
        {
          title: '用户管理',
          url: '/users',
          icon: IconUsers,
        },
        {
          title: '设置中心',
          icon: IconSettings,
          items: [
            {
              title: '账户信息',
              url: '/settings',
              icon: IconUserCog,
            },
            {
              title: '账户设置',
              url: '/settings/account',
              icon: IconTool,
            },
            {
              title: '页面设置',
              url: '/settings/appearance',
              icon: IconPalette,
            },
            {
              title: '通知设置',
              url: '/settings/notifications',
              icon: IconNotification,
            },
            {
              title: '显示设置',
              url: '/settings/display',
              icon: IconBrowserCheck,
            },
          ],
        },
      ],
    },
  ],
}
