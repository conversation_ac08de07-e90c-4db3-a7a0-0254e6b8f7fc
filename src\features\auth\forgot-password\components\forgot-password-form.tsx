import { HTMLAttributes, useState } from 'react'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Link } from '@tanstack/react-router'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'

type ForgotPasswordFormProps = HTMLAttributes<HTMLFormElement>

const formSchema = z.object({
  email: z.string().email({ message: '请输入有效的邮箱地址' }),
})

export function ForgotPasswordForm({
  className,
  ...props
}: ForgotPasswordFormProps) {
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
    },
  })

  function onSubmit(_data: z.infer<typeof formSchema>) {
    setIsLoading(true)

    // 模拟发送重置密码邮件
    setTimeout(() => {
      toast.success('重置密码链接已发送到您的邮箱！')
      setIsLoading(false)
    }, 2000)
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className={cn('grid gap-3', className)}
        {...props}
      >
        <FormField
          control={form.control}
          name='email'
          render={({ field }) => (
            <FormItem>
              <FormLabel>邮箱</FormLabel>
              <FormControl>
                <Input placeholder='<EMAIL>' {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button className='mt-2' disabled={isLoading}>
          {isLoading ? '发送中...' : '发送重置链接'}
        </Button>
        <div className='text-center'>
          <Link
            to='/sign-in'
            className='text-muted-foreground text-sm font-medium hover:opacity-75'
          >
            返回登录
          </Link>
        </div>
      </form>
    </Form>
  )
}
