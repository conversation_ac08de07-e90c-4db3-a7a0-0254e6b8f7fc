import path from 'path'
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
import tailwindcss from '@tailwindcss/vite'
import { tanstackRouter } from '@tanstack/router-plugin/vite'
import { visualizer } from 'rollup-plugin-visualizer'
import compression from 'vite-plugin-compression'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    tanstackRouter({
      routesDirectory: './src/routes',
      generatedRouteTree: './src/routeTree.gen.ts',
      routeFileIgnorePrefix: '-',
      quoteStyle: 'single',
      autoCodeSplitting: true,
    }),

    react({
      // SWC 优化配置
      jsxImportSource: '@emotion/react',
      plugins: [],
    }),

    tailwindcss(),

    // 条件性加载压缩插件（避免开发环境不必要的压缩）
    ...(process.env.NODE_ENV === 'production'
      ? [
          // Gzip 压缩
          compression({
            algorithm: 'gzip',
            ext: '.gz',
            threshold: 1024, // 只压缩大于 1KB 的文件
            deleteOriginFile: false, // 保留原文件
            verbose: true, // 显示压缩信息
          }),

          // Brotli 压缩（更好的压缩率）
          compression({
            algorithm: 'brotliCompress',
            ext: '.br',
            threshold: 1024,
            deleteOriginFile: false,
            verbose: true,
          }),
        ]
      : []),

    // 构建分析工具（只在需要时启用）
    ...(process.env.ANALYZE
      ? [
          visualizer({
            filename: 'dist/stats.html',
            open: true, // 自动打开分析报告
            gzipSize: true,
            brotliSize: true,
            template: 'treemap', // 可选: 'sunburst', 'treemap', 'network'
          }),
        ]
      : []),
  ] as PluginOption[],

  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/pages': path.resolve(__dirname, './src/pages'),
      '@/routes': path.resolve(__dirname, './src/routes'),
      '@/hooks': path.resolve(__dirname, './src/hooks'),
      '@/utils': path.resolve(__dirname, './src/utils'),
      '@/lib': path.resolve(__dirname, './src/lib'),
      '@/types': path.resolve(__dirname, './src/types'),
      '@/stores': path.resolve(__dirname, './src/stores'),
      '@/services': path.resolve(__dirname, './src/services'),
      '@/assets': path.resolve(__dirname, './src/assets'),
      '@/styles': path.resolve(__dirname, './src/styles'),

      // 修复 Tabler Icons 加载问题
      '@tabler/icons-react': '@tabler/icons-react/dist/esm/icons/index.mjs',
    },
  },

  server: {
    host: '0.0.0.0', // 允许局域网访问
    port: 3000,
    open: true,
    cors: true,

    // 热更新配置
    hmr: {
      overlay: true, // 显示错误覆盖层
      port: 24678, // HMR 端口
    },

    // API 代理配置
    proxy: {
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
        // 代理请求日志
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('proxy error', err)
          })
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Sending Request to the Target:', req.method, req.url)
          })
        },
      },
    },

    // 预热常用文件
    warmup: {
      clientFiles: ['./src/main.tsx', './src/App.tsx'],
    },
  },

  // 预览服务器配置
  preview: {
    port: 4173,
    host: '0.0.0.0',
    cors: true,
  },

  build: {
    target: 'esnext',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false, // 生产环境不生成 sourcemap

    // 关键修复：明确指定压缩工具
    minify: process.env.NODE_ENV === 'production' ? 'terser' : 'esbuild',

    // CSS 代码分割
    cssCodeSplit: true,

    // 构建报告
    reportCompressedSize: true,

    // 分块大小警告
    chunkSizeWarningLimit: 1000,

    rollupOptions: {
      output: {
        // 智能分包策略
        manualChunks: (id) => {
          // React 核心库
          if (id.includes('react') || id.includes('react-dom')) {
            return 'react-vendor'
          }

          // TanStack 相关
          if (id.includes('@tanstack/react-router')) {
            return 'router'
          }
          if (id.includes('@tanstack/react-query')) {
            return 'query'
          }

          // UI 组件库
          if (id.includes('@radix-ui') || id.includes('radix-ui')) {
            return 'ui'
          }

          // 图标库
          if (id.includes('lucide-react') || id.includes('@tabler/icons')) {
            return 'icons'
          }

          // 工具库
          if (
            id.includes('lodash') ||
            id.includes('dayjs') ||
            id.includes('date-fns')
          ) {
            return 'utils'
          }

          // HTTP 客户端
          if (id.includes('axios') || id.includes('ky')) {
            return 'http'
          }

          // 表单库
          if (id.includes('react-hook-form') || id.includes('zod')) {
            return 'forms'
          }

          // 状态管理
          if (
            id.includes('zustand') ||
            id.includes('jotai') ||
            id.includes('valtio')
          ) {
            return 'state'
          }

          // 图表库
          if (
            id.includes('recharts') ||
            id.includes('chart.js') ||
            id.includes('d3')
          ) {
            return 'charts'
          }

          // 其他第三方库
          if (id.includes('node_modules')) {
            return 'vendor'
          }
        },

        // 文件命名策略
        chunkFileNames: (chunkInfo) => {
          return 'js/[name]-[hash].js'
        },
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          // 根据文件类型分类
          const info = assetInfo.name?.split('.') || []
          const ext = info[info.length - 1] || 'asset'

          if (
            /\.(png|jpe?g|gif|svg|webp|ico|avif)$/i.test(assetInfo.name || '')
          ) {
            return 'images/[name]-[hash].[ext]'
          }
          if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name || '')) {
            return 'fonts/[name]-[hash].[ext]'
          }
          if (
            /\.(mp4|webm|ogg|mp3|wav|flac|aac)$/i.test(assetInfo.name || '')
          ) {
            return 'media/[name]-[hash].[ext]'
          }
          if (ext === 'css') {
            return 'css/[name]-[hash].[ext]'
          }

          return 'assets/[name]-[hash].[ext]'
        },
      },

      // 忽略特定警告
      onwarn(warning, warn) {
        if (warning.code === 'THIS_IS_UNDEFINED') return
        if (warning.code === 'CIRCULAR_DEPENDENCY') return
        warn(warning)
      },
    },

    // Terser 压缩配置（当 minify: 'terser' 时生效）
    terserOptions: {
      compress: {
        drop_console: true, // 移除 console
        drop_debugger: true, // 移除 debugger
        pure_funcs: ['console.log', 'console.info'], // 移除特定函数
        dead_code: true, // 移除无用代码
        unused: true, // 移除无用变量
      },
      mangle: {
        safari10: true, // Safari 10 兼容性
      },
      format: {
        comments: false, // 移除注释
      },
    },
  },

  // CSS 配置
  css: {
    devSourcemap: process.env.NODE_ENV === 'development',

    // CSS 模块配置
    modules: {
      localsConvention: 'camelCase',
      generateScopedName:
        process.env.NODE_ENV === 'development'
          ? '[name]__[local]___[hash:base64:5]'
          : '[hash:base64:8]',
    },

    // PostCSS 配置
    postcss: {
      plugins: [],
    },
  },

  // 依赖预构建优化
  optimizeDeps: {
    // 强制预构建
    include: ['react', 'react-dom', '@tanstack/react-router', 'lucide-react'],

    // 排除预构建
    exclude: ['@tanstack/router-plugin'],

    // esbuild 选项
    esbuildOptions: {
      target: 'esnext',
      format: 'esm',
    },
  },

  // ESBuild 配置
  esbuild: {
    target: 'esnext',
    // 生产环境移除 console 和 debugger
    drop: process.env.NODE_ENV === 'production' ? ['console', 'debugger'] : [],
    legalComments: 'none', // 移除许可证注释
    charset: 'utf8',
    minifyIdentifiers: process.env.NODE_ENV === 'production',
    minifySyntax: process.env.NODE_ENV === 'production',
    minifyWhitespace: process.env.NODE_ENV === 'production',
  },

  // 环境变量
  define: {
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
    __PROD__: JSON.stringify(process.env.NODE_ENV === 'production'),
    __VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
    __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
  },

  // 日志级别
  logLevel: 'info',

  // 构建时清屏
  clearScreen: true,
})
