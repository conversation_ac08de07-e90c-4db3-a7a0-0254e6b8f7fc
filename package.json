{"name": "shadcn-admin", "private": false, "version": "1.4.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "format:check": "prettier --check .", "format": "prettier --write .", "knip": "knip"}, "dependencies": {"@clerk/clerk-react": "^5.32.1", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.34.0", "@tailwindcss/vite": "^4.1.10", "@tanstack/react-query": "^5.81.2", "@tanstack/react-router": "^1.121.34", "@tanstack/react-table": "^8.21.3", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "date-fns": "^4.1.0", "input-otp": "^1.4.2", "js-cookie": "^3.0.5", "lucide-react": "^0.523.0", "react": "^19.1.0", "react-day-picker": "9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-top-loading-bar": "^3.0.2", "recharts": "^3.0.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "tw-animate-css": "^1.3.4", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.29.0", "@faker-js/faker": "^9.8.0", "@tanstack/eslint-plugin-query": "^5.81.2", "@tanstack/react-query-devtools": "^5.81.2", "@tanstack/react-router-devtools": "^1.121.34", "@tanstack/router-plugin": "^1.121.34", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/js-cookie": "^3.0.6", "@types/node": "^24.0.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.10.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "knip": "^5.61.2", "prettier": "^3.6.0", "prettier-plugin-tailwindcss": "^0.6.13", "typescript": "~5.8.3", "typescript-eslint": "^8.35.0", "vite": "^7.0.0"}}